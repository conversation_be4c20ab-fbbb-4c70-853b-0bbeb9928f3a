<?php

namespace App\Console\Commands\Data;

use App\Jobs\NormalizeDomainBatchJob;
use App\Models\StoreLeads\Domain;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SLNormalizeDomainsQueue extends Command
{
    protected $signature = 'data:sl-normalize-domains-queue 
                            {--batch-size=1000 : Number of domains to process per job}
                            {--max-jobs=50 : Maximum number of jobs to dispatch initially}
                            {--dry-run : Show what would be processed without actually dispatching jobs}';

    protected $description = 'Queue domain normalization jobs for processing root_domain field';

    public function handle()
    {
        $batchSize = (int) $this->option('batch-size');
        $maxJobs = (int) $this->option('max-jobs');
        $dryRun = $this->option('dry-run');

        // Count total domains to process
        $totalDomains = Domain::whereNull('root_domain')->count();
        
        if ($totalDomains === 0) {
            $this->info('No domains found that need root_domain normalization.');
            return;
        }

        $totalJobs = ceil($totalDomains / $batchSize);
        $jobsToDispatch = min($maxJobs, $totalJobs);

        $this->info("Found {$totalDomains} domains to process");
        $this->info("Will create {$totalJobs} total jobs ({$batchSize} domains per job)");
        $this->info("Dispatching first {$jobsToDispatch} jobs now");

        if ($dryRun) {
            $this->warn('DRY RUN - No jobs will be dispatched');
            return;
        }

        // Dispatch initial batch of jobs
        for ($i = 0; $i < $jobsToDispatch; $i++) {
            $offset = $i * $batchSize;
            $shouldDispatchNext = ($i === $jobsToDispatch - 1); // Only the last job should dispatch next batches
            
            NormalizeDomainBatchJob::dispatch($batchSize, $offset, $shouldDispatchNext)
                ->delay(now()->addSeconds($i * 2)); // Stagger job starts
        }

        $this->info("Dispatched {$jobsToDispatch} jobs to the queue");
        $this->info("Jobs will automatically chain to process all {$totalDomains} domains");
        $this->warn("Make sure your queue workers are running: php artisan queue:work");
        
        Log::info("SLNormalizeDomainsQueue: Started processing {$totalDomains} domains with {$jobsToDispatch} initial jobs");
    }
}
