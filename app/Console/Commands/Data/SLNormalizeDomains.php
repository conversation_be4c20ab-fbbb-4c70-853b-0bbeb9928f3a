<?php

namespace App\Console\Commands\Data;

use App\Jobs\NormalizeDomainBatchJob;
use App\Models\StoreLeads\Domain;
use Illuminate\Console\Command;

class SLNormalizeDomains extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:sl-normalize-domains';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Normalize store leads domain names and extract root_domain';

    protected const RUN_LIMIT = 20000;

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $domainNormalizer = app(DomainNormalizationService::class);
        $processedCount = 0;

        Domain::whereNull('root_domain')
            ->chunk(1000, function ($domains) use (&$processedCount, $domainNormalizer) {
                foreach ($domains as $domain) {
                    $url = urlTrim($domain->name);
                    $rootDomain = $domainNormalizer->parseAndNormalize($url);
                    if ($url == $rootDomain) {
                        // If the URL is already a root domain, we can use it directly (e.g. john.myshopify.com because our normalizer detects myshopify.com as a platform url)
                        Domain::where('id', $domain->id)->update(['root_domain' => $url]);
                        $processedCount++;
                    } else {
                        $platformHost = PlatformHost::where('registrable', $rootDomain)->first();
                        if (empty($platformHost)) {
                            $this->error("normalizeDomains: Root domain: $rootDomain of domain {$domain->name} not found in platform hosts.");
                            Log::error("normalizeDomains: Root domain: $rootDomain of domain {$domain->name} not found in platform hosts.");
                            // TODO: we need to handle this case. It can be one of 2 cases:
                            // 1. a root domain with less than 10 subdomains (so we skipped saving it in platform hosts)
                            // 2. it failed to be parsed and checked for subdomains (normalizer failed due to Invalid domain name - can be false alarm, most cases are domains containing an underscore, which is not allowed in domain names)
                            // Easier fix is to use the $url as the root domain, meaning we will try to find contacts using the full URL
                            continue;
                        }
                        if ($platformHost->is_platform === true) {
                            // example: kate.bigcartel.com, this is a shop hosted on bigcartel.com, we need to contact kate, so use the full url
                            // example: john.mytradefit.com, this is a hosted shop/page on a platform and we need to contact john, so use the full url
                            Domain::where('id', $domain->id)->update(['root_domain' => $url]);
                            $processedCount++;
                        } elseif ($platformHost->is_platform === false) {
                            // example: en.garage.com, this is a subdomain so use root domain
                            // example: jim.scentsy.us, this looks like an e-shop but is just a site having many users with their own webpage, so we contact the root domain
                            Domain::where('id', $domain->id)->update(['root_domain' => $rootDomain]);
                            $processedCount++;
                        } else {
                            // platformHost->is_platform === null
                            // not scanned yet, so we skip
                            continue;
                        }
                    }
                }
                if ($processedCount >= self::RUN_LIMIT) {
                    $this->info("Processed $processedCount domains, stopping to avoid long execution time.");
                    return false; // Stop the chunk processing
                }
            });
    }
}
