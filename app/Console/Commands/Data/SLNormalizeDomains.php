<?php

namespace App\Console\Commands\Data;

use App\Jobs\DbMigrations\NormalizeDomain;
use App\Models\StoreLeads\Domain;
use Illuminate\Console\Command;

class SLNormalizeDomains extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:sl-normalize-domains {--start-id=0 : Domain ID to start from}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch job to normalize store leads domain names and extract root_domain';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $startId = (int) $this->option('start-id');

        // Count total domains to process
        $totalDomains = Domain::whereNull('root_domain')
            ->where('id', '>', $startId)
            ->count();

        if ($totalDomains === 0) {
            $this->info('No domains found that need root_domain normalization.');
            return;
        }

        $this->info("Found {$totalDomains} domains to process starting from ID {$startId}");
        $this->info("Dispatching job to queue...");

        // Dispatch the job
        NormalizeDomain::dispatch($startId);

        $this->info("Job dispatched successfully!");
    }
}
