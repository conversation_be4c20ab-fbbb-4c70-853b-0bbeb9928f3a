<?php

namespace App\Jobs\DbMigrations;

use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\PlatformHost;
use App\Services\DomainNormalizationService;
use App\Traits\LogsMessages;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class NormalizeDomain implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $timeout = 3600; // 1 hour timeout
    public $tries = 3;

    private const BATCH_SIZE = 20000;

    protected $lastId;

    public function __construct($lastId = 0)
    {
        $this->lastId = $lastId;
        $this->logPrefix = "NormalizeDomain: ";
    }

    public function handle()
    {
        $this->logInfo('Starting. Last ID: ' . $this->lastId);

        $domainNormalizer = app(DomainNormalizationService::class);
        $processedCount = 0;
        $lastId = $this->lastId;

        while ($processedCount < self::BATCH_SIZE) {
            $hasRecords = false;

            Domain::whereNull('root_domain')
                ->where('id', '>', $lastId)
                ->orderBy('id')
                ->chunk(1000, function ($domains) use (&$processedCount, &$lastId, &$hasRecords, $domainNormalizer) {
                    foreach ($domains as $domain) {
                        if ($processedCount >= self::BATCH_SIZE) {
                            return false;
                        }

                        $lastId = $domain->id;
                        $hasRecords = true;

                        try {
                            $url = urlTrim($domain->name);

                            try {
                                // Normalize the URL to ensure it is in a valid format
                                $parsed = $domainNormalizer->parseAndNormalize($url);
                                $rootDomain = $parsed['root_domain'];

                            } catch (\Exception $e) {
                                $this->logError("Failed to normalize URL for domain {$domain->name}: " . $e->getMessage());

                                $rootDomain = $url; // Fallback to the original URL if normalization fails
                            }

                            if ($url == $rootDomain) {
                                // If the URL is already a root domain, we can use it directly
                                Domain::where('id', $domain->id)->update(['root_domain' => $url]);
                                $processedCount++;
                            } else {
                                $platformHost = PlatformHost::where('registrable', $rootDomain)->first();
                                if (empty($platformHost)) {
                                    // This means that it's a subdomain of a less than 10 subdomains found for root domain, or that the domain failed to be normalized (because on underscores etc.)
                                    // In this case we can use the original URL as root domain
                                    Domain::where('id', $domain->id)->update(['root_domain' => $url]);
                                    $processedCount++;
                                }

                                if ($platformHost->is_platform === true) {
                                    // example: kate.bigcartel.com, this is a shop hosted on bigcartel.com, we need to contact kate, so use the full url
                                    Domain::where('id', $domain->id)->update(['root_domain' => $url]);
                                    $processedCount++;
                                } elseif ($platformHost->is_platform === false) {
                                    // example: en.garage.com, this is a subdomain so use root domain
                                    Domain::where('id', $domain->id)->update(['root_domain' => $rootDomain]);
                                    $processedCount++;
                                } else {
                                    // platformHost->is_platform === null - not scanned yet, so we skip
                                    continue;
                                }
                            }
                        } catch (\Exception $e) {
                            $this->logError("Failed to process domain {$domain->name}: " . $e->getMessage());
                            continue;
                        }
                    }

                    $this->logInfo('Processed. ID: ' . $lastId);
                });

            if (!$hasRecords || $processedCount >= self::BATCH_SIZE) {
                break;
            }
        }

        $this->logInfo('Finished. Processed: ' . $processedCount . '. Last ID: ' . $lastId);

        // If we processed any records and there might be more, dispatch next batch
        if ($processedCount > 0) {
            self::dispatch($lastId)->delay(now()->addSeconds(20));
        }
    }
}
