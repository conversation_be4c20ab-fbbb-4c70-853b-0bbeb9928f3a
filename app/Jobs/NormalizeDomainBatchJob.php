<?php

namespace App\Jobs;

use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\PlatformHost;
use App\Services\DomainNormalizationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NormalizeDomainBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    private int $batchSize;
    private int $offset;
    private bool $shouldDispatchNext;

    public function __construct(int $batchSize = 1000, int $offset = 0, bool $shouldDispatchNext = true)
    {
        $this->batchSize = $batchSize;
        $this->offset = $offset;
        $this->shouldDispatchNext = $shouldDispatchNext;
    }

    public function handle()
    {
        $domainNormalizer = app(DomainNormalizationService::class);
        $platformHostCache = [];
        $updates = [];
        $processedCount = 0;

        // Get batch of domains
        $domains = Domain::whereNull('root_domain')
            ->offset($this->offset)
            ->limit($this->batchSize)
            ->get(['id', 'name']);

        if ($domains->isEmpty()) {
            Log::info("NormalizeDomainBatchJob: No more domains to process at offset {$this->offset}");
            return;
        }

        foreach ($domains as $domain) {
            try {
                $url = urlTrim($domain->name);
                $parsed = $domainNormalizer->parseAndNormalize($url);
                $rootDomain = $parsed['root_domain'];

                if ($url == $rootDomain) {
                    $updates[] = ['id' => $domain->id, 'root_domain' => $url];
                    $processedCount++;
                } else {
                    // Cache platform host lookups
                    if (!isset($platformHostCache[$rootDomain])) {
                        $platformHostCache[$rootDomain] = PlatformHost::where('registrable', $rootDomain)->first();
                    }

                    $platformHost = $platformHostCache[$rootDomain];
                    if (empty($platformHost)) {
                        Log::warning("NormalizeDomainBatchJob: Root domain $rootDomain not found in platform hosts for domain {$domain->name}");
                        // Use full URL as fallback
                        $updates[] = ['id' => $domain->id, 'root_domain' => $url];
                        $processedCount++;
                        continue;
                    }

                    if ($platformHost->is_platform === true) {
                        $updates[] = ['id' => $domain->id, 'root_domain' => $url];
                        $processedCount++;
                    } elseif ($platformHost->is_platform === false) {
                        $updates[] = ['id' => $domain->id, 'root_domain' => $rootDomain];
                        $processedCount++;
                    }
                    // Skip if is_platform is null
                }
            } catch (\Exception $e) {
                Log::error("NormalizeDomainBatchJob: Failed to process domain {$domain->name}", [
                    'error' => $e->getMessage(),
                    'offset' => $this->offset
                ]);
                continue;
            }
        }

        // Batch update using raw SQL for better performance
        if (!empty($updates)) {
            $this->batchUpdateDomains($updates);
        }

        Log::info("NormalizeDomainBatchJob: Processed $processedCount domains at offset {$this->offset}");

        // Dispatch next batch if needed
        if ($this->shouldDispatchNext && count($domains) === $this->batchSize) {
            NormalizeDomainBatchJob::dispatch(
                $this->batchSize,
                $this->offset + $this->batchSize,
                true
            )->delay(now()->addSeconds(2)); // Small delay to prevent overwhelming the queue
        }
    }

    private function batchUpdateDomains(array $updates)
    {
        // Use raw SQL for better performance with large batches
        $cases = [];
        $ids = [];

        foreach ($updates as $update) {
            $cases[] = "WHEN {$update['id']} THEN " . DB::connection()->getPdo()->quote($update['root_domain']);
            $ids[] = $update['id'];
        }

        $idsString = implode(',', $ids);
        $casesString = implode(' ', $cases);

        DB::statement("
            UPDATE sl_domains 
            SET root_domain = CASE id {$casesString} END 
            WHERE id IN ({$idsString})
        ");
    }

    public function failed(\Throwable $exception)
    {
        Log::error("NormalizeDomainBatchJob failed at offset {$this->offset}", [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
