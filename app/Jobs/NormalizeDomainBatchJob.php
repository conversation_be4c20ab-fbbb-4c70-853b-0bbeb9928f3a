<?php

namespace App\Jobs;

use App\Models\StoreLeads\Domain;
use App\Models\StoreLeads\PlatformHost;
use App\Services\DomainNormalizationService;
use App\Traits\LogsMessages;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class NormalizeDomainBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, LogsMessages;

    public $timeout = 3600; // 1 hour timeout
    public $tries = 3;

    private const BATCH_SIZE = 20000;

    protected $lastId;

    public function __construct($lastId = 0)
    {
        $this->lastId = $lastId;
        $this->logPrefix = "NormalizeDomainBatchJob: ";
    }

    public function handle()
    {
        $this->logInfo('Starting. Last ID: ' . $this->lastId);

        $domainNormalizer = app(DomainNormalizationService::class);
        $processedCount = 0;
        $lastId = $this->lastId;
        $platformHostCache = [];

        while ($processedCount < self::BATCH_SIZE) {
            $hasRecords = false;

            Domain::whereNull('root_domain')
                ->where('id', '>', $lastId)
                ->orderBy('id')
                ->chunk(1000, function ($domains) use (&$processedCount, &$lastId, &$hasRecords, $domainNormalizer, &$platformHostCache) {
                    foreach ($domains as $domain) {
                        if ($processedCount >= self::BATCH_SIZE) {
                            return false;
                        }

                        $lastId = $domain->id;
                        $hasRecords = true;

                        try {
                            $url = urlTrim($domain->name);
                            $parsed = $domainNormalizer->parseAndNormalize($url);
                            $rootDomain = $parsed['root_domain'];
                            
                            if ($url == $rootDomain) {
                                // If the URL is already a root domain, we can use it directly
                                Domain::where('id', $domain->id)->update(['root_domain' => $url]);
                                $processedCount++;
                            } else {
                                // Cache platform host lookups to reduce queries
                                if (!isset($platformHostCache[$rootDomain])) {
                                    $platformHostCache[$rootDomain] = PlatformHost::where('registrable', $rootDomain)->first();
                                }
                                
                                $platformHost = $platformHostCache[$rootDomain];
                                if (empty($platformHost)) {
                                    $this->logError("Root domain: $rootDomain of domain {$domain->name} not found in platform hosts.");
                                    // Use the full URL as fallback
                                    continue;
                                }
                                
                                if ($platformHost->is_platform === true) {
                                    // example: kate.bigcartel.com, this is a shop hosted on bigcartel.com, we need to contact kate, so use the full url
                                    Domain::where('id', $domain->id)->update(['root_domain' => $url]);
                                    $processedCount++;
                                } elseif ($platformHost->is_platform === false) {
                                    // example: en.garage.com, this is a subdomain so use root domain
                                    Domain::where('id', $domain->id)->update(['root_domain' => $rootDomain]);
                                    $processedCount++;
                                } else {
                                    // platformHost->is_platform === null - not scanned yet, so we skip
                                    continue;
                                }
                            }
                        } catch (\Exception $e) {
                            $this->logError("Failed to process domain {$domain->name}: " . $e->getMessage());
                            continue;
                        }
                    }

                    $this->logInfo('Processed. ID: ' . $lastId);
                });

            if (!$hasRecords || $processedCount >= self::BATCH_SIZE) {
                break;
            }
        }

        $this->logInfo('Finished. Processed: ' . $processedCount . '. Last ID: ' . $lastId);

        // If we processed any records and there might be more, dispatch next batch
        if ($processedCount > 0) {
            self::dispatch($lastId)->delay(now()->addSeconds(20));
        }
    }
}
